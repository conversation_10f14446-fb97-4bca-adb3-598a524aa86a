"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; }var _chunkYC27MA32cjs = require('./chunk-YC27MA32.cjs');function y(...r){return _chunkYC27MA32cjs.a.call(void 0, s,r)}function s(r,a){let e=Object.create(null);for(let t of r){let o=_optionalChain([t, 'optionalAccess', _ => _[a]]);if(o!==void 0){let n=e[o];n===void 0?e[o]=[t]:n.push(t)}}return Object.setPrototypeOf(e,Object.prototype),e}exports.a = y;
