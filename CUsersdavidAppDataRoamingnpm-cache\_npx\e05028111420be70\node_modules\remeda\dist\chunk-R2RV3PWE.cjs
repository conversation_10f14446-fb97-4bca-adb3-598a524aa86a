"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkYC27MA32cjs = require('./chunk-YC27MA32.cjs');function d(...n){return _chunkYC27MA32cjs.a.call(void 0, u,n)}function u(n,o=[],t=[]){if(typeof n=="function")return n;if(typeof n!="object"||n===null)return structuredClone(n);let r=Object.getPrototypeOf(n);if(!Array.isArray(n)&&r!==null&&r!==Object.prototype)return structuredClone(n);let e=o.indexOf(n);return e!==-1?t[e]:(o.push(n),Array.isArray(n)?p(n,o,t):i(n,o,t))}function i(n,o,t){let r={};t.push(r);for(let[e,c]of Object.entries(n))r[e]=u(c,o,t);return r}function p(n,o,t){let r=[];t.push(r);for(let[e,c]of n.entries())r[e]=u(c,o,t);return r}exports.a = d;
