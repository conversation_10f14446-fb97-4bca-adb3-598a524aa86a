"use strict";Object.defineProperty(exports, "__esModule", {value: true});var o=["	",`
`,"\v","\f","\r"," ","\x85","\xA0","\u1680","\u2000","\u2001","\u2002","\u2003","\u2004","\u2005","\u2006","\u2007","\u2008","\u2009","\u200A","\u2028","\u2029","\u202F","\u205F","\u3000","\uFEFF"],c=new Set(["-","_",...o]),i= exports.a =r=>{let e=[],t="",u=()=>{t.length>0&&(e.push(t),t="")};for(let s of r){if(c.has(s)){u();continue}if(/[a-z]$/u.test(t)&&/[A-Z]/u.test(s))u();else if(/[A-Z][A-Z]$/u.test(t)&&/[a-z]/u.test(s)){let n=t.slice(-1);t=t.slice(0,-1),u(),t=n}else/\d$/u.test(t)!==/\d/u.test(s)&&u();t+=s}return u(),e};exports.a = i;
