"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _nullishCoalesce(lhs, rhsFn) { if (lhs != null) { return lhs; } else { return rhsFn(); } }var _chunkYC27MA32cjs = require('./chunk-YC27MA32.cjs');function r(...t){return _chunkYC27MA32cjs.a.call(void 0, T,t)}function T(t,n,l){let e=t;for(let o of n){if(e==null)break;e=e[o]}return _nullishCoalesce(e, () => (l))}exports.a = r;
