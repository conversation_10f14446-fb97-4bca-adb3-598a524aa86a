"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkYC27MA32cjs = require('./chunk-YC27MA32.cjs');function i(...e){return _chunkYC27MA32cjs.a.call(void 0, p,e)}var p=(e,d)=>{let r=Object.create(null);for(let n=0;n<e.length;n++){let t=e[n],y=d(t,n,e);if(y!==void 0){let o=r[y];o===void 0?r[y]=[t]:o.push(t)}}return Object.setPrototypeOf(r,Object.prototype),r};exports.a = i;
