import { Join, Words } from 'type-fest';

type SnakeCase<S extends string> = string extends S ? string : Lowercase<Join<Words<S>, "_">>;
/**
 * Convert a text to snake-case by splitting it into words and joining them back
 * together with "_", and then lowering the case of the result.
 *
 * For other case manipulations see: `toLowerCase`, `toUpper<PERSON>ase`, `capitalize`,
 * `uncapitalize`, `toCamel<PERSON>ase`, and `toKebabCase`.
 *
 * !IMPORTANT: This function might work _incorrectly_ for **non-ascii** inputs.
 *
 * @param data - A string.
 * @signature
 *   R.toSnakeCase(data);
 * @example
 *   R.toSnakeCase("hello world"); // "hello_world"
 *   R.toSnakeCase("__HELLO_WORLD__"); // "hello_world"
 * @dataFirst
 * @category String
 */
declare function toSnakeCase<S extends string>(data: S): SnakeCase<S>;
/**
 * Convert a text to snake-case by splitting it into words and joining them back
 * together with "_", and then lowering the case of the result.
 *
 * For other case manipulations see: `toLower<PERSON>ase`, `toUpper<PERSON>ase`, `capitalize`,
 * `uncapitalize`, `toCamel<PERSON>ase`, and `to<PERSON>ebabCase`.
 *
 * !IMPORTANT: This function might work _incorrectly_ for **non-ascii** inputs.
 *
 * @signature
 *   R.toSnakeCase()(data);
 * @example
 *   R.pipe("hello world", R.toSnakeCase()); // "hello_world"
 *   R.pipe("__HELLO_WORLD__", toSnakeCase()); // "hello_world"
 * @dataLast
 * @category String
 */
declare function toSnakeCase(): <S extends string>(data: S) => SnakeCase<S>;

export { toSnakeCase };
