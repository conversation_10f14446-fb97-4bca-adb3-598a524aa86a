"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkYC27MA32cjs = require('./chunk-YC27MA32.cjs');function k(...n){return _chunkYC27MA32cjs.a.call(void 0, u,n)}function u(n,e){if(n===e||Object.is(n,e))return!0;if(typeof n!="object"||typeof e!="object"||n===null||e===null||Object.getPrototypeOf(n)!==Object.getPrototypeOf(e))return!1;if(Array.isArray(n))return l(n,e);if(n instanceof Map)return a(n,e);if(n instanceof Set)return c(n,e);if(n instanceof Date)return n.getTime()===e.getTime();if(n instanceof RegExp)return n.toString()===e.toString();if(Object.keys(n).length!==Object.keys(e).length)return!1;for(let[r,t]of Object.entries(n))if(!(r in e)||!u(t,e[r]))return!1;return!0}function l(n,e){if(n.length!==e.length)return!1;for(let[r,t]of n.entries())if(!u(t,e[r]))return!1;return!0}function a(n,e){if(n.size!==e.size)return!1;for(let[r,t]of n.entries())if(!e.has(r)||!u(t,e.get(r)))return!1;return!0}function c(n,e){if(n.size!==e.size)return!1;let r=[...e];for(let t of n){let o=!1;for(let[s,f]of r.entries())if(u(t,f)){o=!0,r.splice(s,1);break}if(!o)return!1}return!0}exports.a = k;
