"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _nullishCoalesce(lhs, rhsFn) { if (lhs != null) { return lhs; } else { return rhsFn(); } }var _chunkZUF6ZE7Ycjs = require('./chunk-ZUF6ZE7Y.cjs');var _chunk7GN7FGBWcjs = require('./chunk-7GN7FGBW.cjs');function s(...t){return _chunkZUF6ZE7Ycjs.a.call(void 0, i,t)}function i(t){if(t.length===0)return _chunk7GN7FGBWcjs.b;let n=new Map;for(let r of t)n.set(r,(_nullishCoalesce(n.get(r), () => (0)))+1);return r=>{let e=n.get(r);return e===void 0||e===0?_chunk7GN7FGBWcjs.a:(e===1?n.delete(r):n.set(r,e-1),{hasNext:!0,next:r,done:n.size===0})}}exports.a = s;
