import{a as d}from"./chunk-DH3BPT6T.js";import{a as u}from"./chunk-AIG3BDKO.js";function T(n,o){for(let r=Math.floor(n.length/2)-1;r>=0;r--)c(n,r,o)}function m(n,o,r){if(!u(n,1))return;let[t]=n;if(!(o(r,t)>=0))return n[0]=r,c(n,0,o),t}function c(n,o,r){let t=o;for(;t*2+1<n.length;){let i=t*2+1,e=r(n[t],n[i])<0?i:t,f=i+1;if(f<n.length&&r(n[e],n[f])<0&&(e=f),e===t)return;d(n,t,e),t=e}}export{T as a,m as b};
