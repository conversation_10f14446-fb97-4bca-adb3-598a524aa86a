"use strict";Object.defineProperty(exports, "__esModule", {value: true});function u(e,a,n){return e(n[0])?t=>a(t,...n):a(...n)}var b=Object.assign(s,{defaultCase:R});function s(...e){return u(l,o,e)}function o(e,...a){for(let n of a){if(typeof n=="function")return n(e);let[t,r]=n;if(t(e))return r(e)}throw new Error("conditional: data failed for all cases")}function l(e){if(!Array.isArray(e))return!1;let[a,n,...t]=e;return typeof a=="function"&&a.length<=1&&typeof n=="function"&&n.length<=1&&t.length===0}function R(e=F){return[T,e]}var T=()=>!0,F=()=>{};exports.a = b;
