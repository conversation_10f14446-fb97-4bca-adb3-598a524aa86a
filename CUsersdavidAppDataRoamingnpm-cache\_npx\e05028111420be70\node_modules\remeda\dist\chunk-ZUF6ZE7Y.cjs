"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkD5PQ5W4Lcjs = require('./chunk-D5PQ5W4L.cjs');function y(t,i){let a=i.length-t.length;if(a===1){let[n,...r]=i;return _chunkD5PQ5W4Lcjs.a.call(void 0, n,{lazy:t,lazyArgs:r})}if(a===0){let n={lazy:t,lazyArgs:i};return Object.assign(e=>_chunkD5PQ5W4Lcjs.a.call(void 0, e,n),n)}throw new Error("Wrong number of arguments")}exports.a = y;
